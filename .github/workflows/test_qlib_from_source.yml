name: Test qlib from source

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    timeout-minutes: 180
    # we may retry for 3 times for `Unit tests with Pytest`

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, ubuntu-24.04, ubuntu-22.04, macos-14, macos-15]
        # In github action, using python 3.7, pip install will not match the latest version of the package.
        # Also, python 3.7 is no longer supported from macos-14, and will be phased out from macos-13 in the near future.
        # All things considered, we have removed python 3.7.
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
    - name: Test qlib from source
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Update pip to the latest version
      run: |
        python -m pip install --upgrade pip

    - name: Installing pytorch for macos
      if: ${{ matrix.os == 'macos-14' || matrix.os == 'macos-15' }}
      run: |
        python -m pip install torch torchvision torchaudio

    - name: Installing pytorch for ubuntu
      if: ${{ matrix.os == 'ubuntu-24.04' || matrix.os == 'ubuntu-22.04' }}
      run: |
        python -m pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cpu

    - name: Installing pytorch for windows
      if: ${{ matrix.os == 'windows-latest' }}
      run: |
        python -m pip install torch torchvision torchaudio

    - name: Set up Python tools
      run: |
        make dev

    - name: Lint with Black
      run: |
        make black

    - name: Make html with sphinx
      # Since read the docs builds on ubuntu 22.04, we only need to test that the build passes on ubuntu 22.04.
      if: ${{ matrix.os == 'ubuntu-22.04' }}
      run: |
        make docs-gen

    - name: Check Qlib with pylint
      run: |
        make pylint

    - name: Check Qlib with flake8
      run: |
        make flake8

    - name: Check Qlib with mypy
      run: |
        make mypy
    
    - name: Check Qlib ipynb with nbqa
      run: |
        make nbqa

    - name: Test data downloads
      run: |
        python scripts/get_data.py qlib_data --name qlib_data_simple --target_dir ~/.qlib/qlib_data/cn_data --interval 1d --region cn
        python scripts/get_data.py download_data --file_name rl_data.zip --target_dir tests/.data/rl

    - name: Install Lightgbm for MacOS
      if: ${{ matrix.os == 'macos-14' || matrix.os == 'macos-15' }}
      run: |
        brew update
        brew install libomp || brew reinstall libomp
        python -m pip install --no-binary=:all: lightgbm

    - name: Check Qlib ipynb with nbconvert
      run: |
        make nbconvert

    - name: Test workflow by config (install from source)
      run: |
        python -m pip install numba
        python qlib/cli/run.py examples/benchmarks/LightGBM/workflow_config_lightgbm_Alpha158.yaml

    - name: Unit tests with Pytest (MacOS)
      if: ${{ matrix.os == 'macos-14' || matrix.os == 'macos-15' }}
      uses: nick-fields/retry@v2
      with:
        timeout_minutes: 60
        max_attempts: 3
        command: |
          # Limit the number of threads in various libraries to prevent Segmentation faults caused by OpenMP multithreading conflicts under macOS.
          export OMP_NUM_THREADS=1  # Limit the number of OpenMP threads
          export MKL_NUM_THREADS=1  # Limit the number of Intel MKL threads
          export NUMEXPR_NUM_THREADS=1  # Limit the number of NumExpr threads
          export OPENBLAS_NUM_THREADS=1  # Limit the number of OpenBLAS threads
          export VECLIB_MAXIMUM_THREADS=1  # Limit the number of macOS Accelerate/vecLib threads
          cd tests
          python -m pytest . -m "not slow" --durations=0

    - name: Unit tests with Pytest (Ubuntu and Windows)
      if: ${{ matrix.os != 'macos-13' && matrix.os != 'macos-14' && matrix.os != 'macos-15' }}
      uses: nick-fields/retry@v2
      with:
        timeout_minutes: 60
        max_attempts: 3
        command: |
          cd tests
          python -m pytest . -m "not slow" --durations=0
