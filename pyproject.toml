[build-system]
requires = ["setuptools", "setuptools-scm", "cython", "numpy>=1.24.0"]
build-backend = "setuptools.build_meta"

[project]
classifiers = [
  "Operating System :: POSIX :: Linux",
  "Operating System :: Microsoft :: Windows",
  "Operating System :: MacOS",
  "License :: OSI Approved :: MIT License",
  "Development Status :: 3 - Alpha",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
]
name = "pyqlib"
dynamic = ["version"]
description = "A Quantitative-research Platform"
requires-python = ">=3.8.0"
readme = {file = "README.md", content-type = "text/markdown"}
license = { text = "MIT" }

dependencies = [
  "pyyaml",
  "numpy",
  # Since version 1.1.0, pandas supports the ffill and bfill methods.
  # Since version 2.1.0, pandas has deprecated the method parameter of the fillna method. 
  # qlib has updated the fillna method in PR 1987 and limited the minimum version of pandas.
  "pandas>=1.1",
  # I encoutered an Error that the set_uri does not work when downloading artifacts in mlflow 3.1.1;
  # But earlier versions of mlflow does not have this problem.
  # But when I switch to 2.*.* version, another error occurs, which is even more strange...
  "mlflow",
  "filelock>=3.16.0",
  "redis",
  "dill",
  "fire",
  "ruamel.yaml>=0.17.38",
  "python-redis-lock",
  "tqdm",
  "pymongo",
  "loguru",
  "lightgbm",
  "gym",
  "cvxpy",
  "joblib",
  "matplotlib",
  "jupyter",
  "nbconvert",
  "pyarrow",
  "pydantic-settings",
  "setuptools-scm",
]

[project.optional-dependencies]
dev = [
  "pytest",
  "statsmodels",
]
# On macos-13 system, when using python version greater than or equal to 3.10,
# pytorch can't fully support Numpy version above 2.0, so, when you want to install torch,
# it will limit the version of Numpy less than 2.0.
rl = [
  "tianshou<=0.4.10",
  "torch",
  "numpy<2.0.0",
]
lint = [
  "black",
  "pylint",
  "mypy<1.5.0",
  "flake8",
  "nbqa",
]
# snowballstemmer, a dependency of sphinx, was released on 2025-05-08 with version 3.0.0,
# which causes errors in the build process. So we've limited the version for now.
docs = [
  # After upgrading scipy to version 1.16.0,
  # we encountered ImportError: cannot import name '_lazywhere', in the build documentation,
  # so we restricted the version of scipy to: 1.15.3
  "scipy<=1.15.3",
  "sphinx",
  "sphinx_rtd_theme",
  "readthedocs_sphinx_ext",
  "snowballstemmer<3.0",
]
package = [
  "twine",
  "build",
]
# test_pit dependency packages
test = [
  "yahooquery",
  "baostock",
]
analysis = [
  "plotly",
  "statsmodels",
]

# In the process of releasing a new version, when checking the manylinux package with twine, an error is reported:
# InvalidDistribution: Invalid distribution metadata: unrecognized or malformed field 'license-file'
# To solve this problem, we added license-files here. Refs: https://github.com/pypa/twine/issues/1216
[tool.setuptools]
packages = [
  "qlib",
]
license-files = []

[project.scripts]
qrun = "qlib.cli.run:run"

[tool.setuptools_scm]
local_scheme = "no-local-version"
version_scheme = "guess-next-dev"
